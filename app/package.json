{"name": "proui", "private": true, "version": "1.50.1", "description": "Pro UI", "type": "module", "repository": {"type": "git", "url": "https://gitlab.adm.internal.proteam.iliad.fr/front/proui.git"}, "msw": {"workerDirectory": "public"}, "engines": {"npm": "please use pnpm", "yarn": "please use pnpm", "node": "22", "pnpm": "10"}, "devEngines": {"runtime": {"name": "node", "version": "22", "onFail": "error"}, "packageManager": {"name": "pnpm", "version": "10", "onFail": "error"}}, "pnpm": {"onlyBuiltDependencies": ["@biomejs/biome", "@parcel/watcher", "@sentry/cli", "@swc/core", "esbuild", "lefthook", "msw"]}, "packageManager": "pnpm@10.6.2", "scripts": {"build": "NODE_OPTIONS=--max-old-space-size=5096 vite build", "clean:build": "rm -rf build && pnpm build", "clean:install": "rm -rf node_modules pnpm-lock.yaml && pnpm install", "start": "vite", "clean:start": "rm -rf build node_modules pnpm-lock.yaml && pnpm install && pnpm start", "dev": "vite", "dev:host": "VITE_HOST=true vite", "dev:host:https": "VITE_HTTPS=true VITE_HOST=true vite", "dev:https": "VITE_HTTPS=true vite", "icons": "node scripts/icons.script.js", "knip": "knip", "compile": "tsc", "deadcode": "ts-prune | grep -v '(used in module)'", "biome:lint": "biome lint ./src", "biome:format": "biome format ./src --write", "biome:check": "biome check ./src", "biome:check:apply": "biome check ./src --write", "biome:check:apply:unsafe": "biome check ./src --write --unsafe", "preview": "vite preview", "preview:host": "VITE_HOST=true vite preview", "preview:host:https": "VITE_HTTPS=true VITE_HOST=true vite preview", "preview:https": "VITE_HTTPS=true vite preview", "preview:https:back": "HTTPS=true vite preview", "router:generate": "tsr generate", "router:watch": "tsr watch", "type:check": "tsc --noEmit", "reset": "pnpm reset:vite && pnpm reset:git"}, "dependencies": {"@dicebear/collection": "^9.2.2", "@dicebear/core": "^9.2.2", "@diceui/combobox": "^0.10.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@faker-js/faker": "^9.6.0", "@headlessui/react": "^2.2.1", "@hookform/resolvers": "^4.1.3", "@lukemorales/query-key-factory": "^1.3.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-tooltip": "^1.2.0", "@radix-ui/react-visually-hidden": "^1.1.3", "@sentry/react": "^9.12.0", "@sentry/vite-plugin": "^3.3.1", "@splinetool/react-spline": "^4.0.0", "@splinetool/runtime": "^1.9.82", "@tabler/icons-react": "^3.31.0", "@tanstack/react-query": "^5.72.1", "@tanstack/react-query-devtools": "^5.72.1", "@tanstack/react-ranger": "^0.0.4", "@tanstack/react-router": "^1.115.2", "@tanstack/react-table": "^8.21.2", "@tanstack/router-cli": "^1.115.2", "@tanstack/router-devtools": "^1.115.2", "@tanstack/router-plugin": "^1.115.2", "@tanstack/zod-adapter": "^1.115.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@tools/reactor-icons": "1.1.0", "@tools/reactore": "3.0.6", "@types/mapbox-gl": "^3.4.1", "@udecode/plate": "^46.0.10", "@udecode/plate-basic-elements": "^47.0.0", "@udecode/plate-basic-marks": "^46.0.5", "@udecode/plate-common": "^42.0.0", "@udecode/plate-markdown": "^47.2.1", "@vitejs/plugin-react-swc": "^3.8.1", "add": "^2.0.6", "autoprefixer": "^10.4.21", "axios": "^1.8.4", "axios-retry": "^4.5.0", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "escape-html": "^1.0.3", "html-react-parser": "^5.2.3", "immer": "^10.1.1", "jotai": "^2.12.2", "jotai-immer": "^0.4.1", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "lucide-react": "^0.484.0", "mapbox-gl": "^3.12.0", "pnpm": "^10.9.0", "react": "link:@udecode/plate-basic-elements/react", "react-datepicker": "^8.3.0", "react-day-picker": "8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.0", "react-full-screen": "^1.1.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.55.0", "react-map-gl": "^8.0.4", "react-quill": "^2.0.0", "react-toastify": "^11.0.5", "recharts": "^2.15.2", "sass": "^1.86.3", "slate": "^0.112.0", "slate-history": "^0.110.3", "slate-hyperscript": "^0.100.0", "slate-react": "^0.112.1", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "5.7.3", "uuid": "^11.1.0", "vite": "^6.2.5", "vite-plugin-checker": "^0.9.1", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.4", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@faker-js/faker": "^9.6.0", "@hookform/devtools": "^4.4.0", "@swc-jotai/react-refresh": "^0.3.0", "@tailwindcss/forms": "^0.5.10", "@tanstack/react-query-devtools": "^5.69.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.16", "@types/node": "^22.14.0", "@types/prismjs": "^1.26.5", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.2", "@types/sjcl": "^1.0.34", "ansi-colors": "^4.1.3", "ansi-escapes": "^7.0.0", "cli-progress": "^3.12.0", "dexie": "^4.0.11", "execa": "^9.5.2", "jose": "^6.0.10", "jotai-devtools": "^0.11.0", "knip": "^5.48.0", "lefthook": "^1.11.8", "listr": "^0.14.3", "msw": "^2.7.3", "postcss-focus-visible": "^10.0.1", "prismjs": "^1.30.0", "sass": "^1.86.0", "shelljs": "^0.9.2", "sjcl": "^1.0.8", "strip-ansi": "^7.1.0", "tinify": "^1.8.0", "vite-plugin-pwa": "^0.21.2", "workbox-build": "^7.3.0", "workbox-window": "^7.3.0", "yargs": "^17.7.2"}}