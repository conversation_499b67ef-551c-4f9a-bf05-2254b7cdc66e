











































## [1.45.1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.45.0...1.45.1) (2025-05-19)

### 🐛 Fixes
- Pagination REACTOR visible | ([5749d34](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/5749d34e2932ec56c2c96535b51a71a9baf57be9 ))
 | ([PRO-1442](https://app.clickup.com/t/42603198/PRO-1442))
- Pagination REACTOR visible | ([082a351](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/082a35144fa053926c08fb06c7ac9eccbf3f761a ))
 | ([PRO-1442](https://app.clickup.com/t/42603198/PRO-1442))
- input search order | ([fe025ef](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/fe025ef12f5a64dc51f0914a95952f7a10fc88bb ))
 | ([PRO-1437](https://app.clickup.com/t/42603198/PRO-1437))
- input search order | ([a20d1d0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/a20d1d065e212574aa736ba2160e562fa94ac087 ))
 | ([PRO-1437](https://app.clickup.com/t/42603198/PRO-1437))

## [1.45.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.44.2...1.45.0) (2025-05-13)

### ✨ Features
- Redirect tech fiber to fiber | ([600a240](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/600a240d8ab405ffcaafe2b08f48aa59be4d5e67 ))


### 🚑 Bug Hot Fixes
- Redirect tech fiber to fiber | ([aed1a21](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/aed1a2190f126bc7df15789797a646040b875a73 ))


## [1.44.2](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.44.1...1.44.2) (2025-05-06)

### 🐛 Fixes
- col input naming | ([515ae71](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/515ae71d68a32fcddb6abe3a0547f3ba74e90d8e ))
 | ([PRO-1241](https://app.clickup.com/t/42603198/PRO-1241))
- col input | ([e3035c5](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/e3035c573db270e96e061259898045d4c6c03020 ))
 | ([PRO-1241](https://app.clickup.com/t/42603198/PRO-1241))

## [1.44.1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.44.0...1.44.1) (2025-04-30)

### 🐛 Fixes
- color badges status | ([7a248fa](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/7a248fa21694a97a7350c6948e620a19f5061fd5 ))

- update calendar shadcnui | ([da359d4](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/da359d469c8b149f52d7835d80c3d1e50e94f098 ))

- status filter | ([4cbfb1c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/4cbfb1cc71cdf7a8f5a52bc5b631f4d027642e5e ))


## [1.44.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.43.1...1.44.0) (2025-04-30)

### ✨ Features
- status error fiber | ([cd1ff5d](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/cd1ff5dd969a1215c0fa504384138563632fb708 ))

- fixes, format date, status fiber hideen sync, logo color for prod | ([6eeb563](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/6eeb563019f98fda2313cf6af842dfe3796d98b2 ))

- status error fiber | ([066a7bc](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/066a7bc747531be1759b4eeb3907ee2b984060ee ))


## [1.43.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.43.1...1.43.0) (2025-04-30)

### ✨ Features
- don't display GTR GTI on solved tickets | ([5eb1734](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/5eb1734d4e8f18cd195170d430f491ef9be4c893 ))

- added image mailing | ([4bac8be](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/4bac8bef234ba3f5e383c2c426ba3465c5b8af3b ))

- set team in switcher | ([bcb4bc7](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/bcb4bc776503ce007a518d873dccd3aaeaae0a29 ))

- redirection ticket fiber | ([53ce29f](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/53ce29f4e621fcd5e08cd4d42b27c214c50e8820 ))

- id ticket gta | ([cd40a1a](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/cd40a1a53723a2202910ff9f98e8375bc2d82c8f ))

- reject closure modal action | ([eeca563](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/eeca563bfca9353f41b95a3347a37f2914ff68e6 ))

- events/histoy merged | ([64ebdb1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/64ebdb16c823e1f6ba67a71e80ddcaf5ffdc96b6 ))

- display event and transition in event dropdown | ([2055f20](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/2055f202758be527d3a6eec2a01a23169b350f08 ))

- display team even if just one | ([baed071](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/baed071abaa4df3314ee8c1eb177e2f725d479b6 ))

- don't display events in error state | ([a0c615e](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/a0c615e9e5e4b3b22f35595894eaa9156fe7b9f5 ))

- action champ team + reminder | ([001ad87](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/001ad878ea06840c2f17c5bff9eed7130aaf2586 ))

- shortcut markdown | ([de04e28](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/de04e2820f22db458adc02dfa18a346105cf9364 ))

- dark theme update | ([9b3db2f](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/9b3db2f194bfdb604e93200ccdb30b85c9c90581 ))

- multi docs backward compatibility fiber | ([dddc2c0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/dddc2c0dead040c46221818392c63dedf5583113 ))

- bottom chat indicator | ([e7146d5](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/e7146d5b73404cbb2199cdc554d7c40933a80f39 ))

- team switch update ticket atom init | ([22e001d](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/22e001dc57dc5d3cfc67269c988a0bb53859fdb2 ))

- localstorage keep user logged in | ([ecfc8c6](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ecfc8c6fc8a91c5abf05b24f726110167032159a ))

- save team state | ([02a7c86](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/02a7c8663d04bb6722172b261af62ad658cd87df ))

- eternal id outsources | ([55ecae2](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/55ecae26d66c98580f50c608fdc59c2a6ff474c6 ))

- close modal ticket create | ([5b87356](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/5b87356ebc928b72d7263deb7d4463c7fe02aa82 ))

- order created by first on top | ([0cd5f57](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/0cd5f573ce445135ecabf28e2fbba65fa176fe9c ))


### 🐛 Fixes
- call magnet link | ([0e7ca47](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/0e7ca475c9a54c96eb9dfe9f5e6051f55c56c24c ))


## [1.42.4](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.43.0...1.42.4) (2025-04-29)

### ✨ Features
- don't display GTR GTI on solved tickets | ([5eb1734](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/5eb1734d4e8f18cd195170d430f491ef9be4c893 ))

- added image mailing | ([4bac8be](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/4bac8bef234ba3f5e383c2c426ba3465c5b8af3b ))

- set team in switcher | ([bcb4bc7](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/bcb4bc776503ce007a518d873dccd3aaeaae0a29 ))

- redirection ticket fiber | ([53ce29f](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/53ce29f4e621fcd5e08cd4d42b27c214c50e8820 ))

- id ticket gta | ([cd40a1a](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/cd40a1a53723a2202910ff9f98e8375bc2d82c8f ))

- reject closure modal action | ([eeca563](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/eeca563bfca9353f41b95a3347a37f2914ff68e6 ))

- events/histoy merged | ([64ebdb1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/64ebdb16c823e1f6ba67a71e80ddcaf5ffdc96b6 ))

- display event and transition in event dropdown | ([2055f20](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/2055f202758be527d3a6eec2a01a23169b350f08 ))

- display team even if just one | ([baed071](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/baed071abaa4df3314ee8c1eb177e2f725d479b6 ))

- don't display events in error state | ([a0c615e](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/a0c615e9e5e4b3b22f35595894eaa9156fe7b9f5 ))

- action champ team + reminder | ([001ad87](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/001ad878ea06840c2f17c5bff9eed7130aaf2586 ))

- shortcut markdown | ([de04e28](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/de04e2820f22db458adc02dfa18a346105cf9364 ))

- dark theme update | ([9b3db2f](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/9b3db2f194bfdb604e93200ccdb30b85c9c90581 ))

- multi docs backward compatibility fiber | ([dddc2c0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/dddc2c0dead040c46221818392c63dedf5583113 ))

- bottom chat indicator | ([e7146d5](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/e7146d5b73404cbb2199cdc554d7c40933a80f39 ))

- team switch update ticket atom init | ([22e001d](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/22e001dc57dc5d3cfc67269c988a0bb53859fdb2 ))

- localstorage keep user logged in | ([ecfc8c6](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ecfc8c6fc8a91c5abf05b24f726110167032159a ))

- save team state | ([02a7c86](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/02a7c8663d04bb6722172b261af62ad658cd87df ))

- eternal id outsources | ([55ecae2](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/55ecae26d66c98580f50c608fdc59c2a6ff474c6 ))

- close modal ticket create | ([5b87356](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/5b87356ebc928b72d7263deb7d4463c7fe02aa82 ))

- order created by first on top | ([0cd5f57](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/0cd5f573ce445135ecabf28e2fbba65fa176fe9c ))


### 🐛 Fixes
- call magnet link | ([0e7ca47](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/0e7ca475c9a54c96eb9dfe9f5e6051f55c56c24c ))


## [1.42.3](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.42.2...1.42.3) (2025-04-29)

### ✨ Features
- don't display GTR GTI on solved tickets | ([5eb1734](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/5eb1734d4e8f18cd195170d430f491ef9be4c893 ))

- added image mailing | ([4bac8be](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/4bac8bef234ba3f5e383c2c426ba3465c5b8af3b ))

- set team in switcher | ([bcb4bc7](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/bcb4bc776503ce007a518d873dccd3aaeaae0a29 ))


## [1.42.1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.42.4...1.42.1) (2025-04-28)

### ✨ Features
- redirection ticket fiber | ([53ce29f](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/53ce29f4e621fcd5e08cd4d42b27c214c50e8820 ))

- id ticket gta | ([cd40a1a](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/cd40a1a53723a2202910ff9f98e8375bc2d82c8f ))

- reject closure modal action | ([eeca563](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/eeca563bfca9353f41b95a3347a37f2914ff68e6 ))

- events/histoy merged | ([64ebdb1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/64ebdb16c823e1f6ba67a71e80ddcaf5ffdc96b6 ))

- display event and transition in event dropdown | ([2055f20](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/2055f202758be527d3a6eec2a01a23169b350f08 ))

- display team even if just one | ([baed071](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/baed071abaa4df3314ee8c1eb177e2f725d479b6 ))

- don't display events in error state | ([a0c615e](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/a0c615e9e5e4b3b22f35595894eaa9156fe7b9f5 ))

- action champ team + reminder | ([001ad87](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/001ad878ea06840c2f17c5bff9eed7130aaf2586 ))

- shortcut markdown | ([de04e28](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/de04e2820f22db458adc02dfa18a346105cf9364 ))

- dark theme update | ([9b3db2f](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/9b3db2f194bfdb604e93200ccdb30b85c9c90581 ))

- multi docs backward compatibility fiber | ([dddc2c0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/dddc2c0dead040c46221818392c63dedf5583113 ))

- bottom chat indicator | ([e7146d5](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/e7146d5b73404cbb2199cdc554d7c40933a80f39 ))

- team switch update ticket atom init | ([22e001d](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/22e001dc57dc5d3cfc67269c988a0bb53859fdb2 ))

- localstorage keep user logged in | ([ecfc8c6](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ecfc8c6fc8a91c5abf05b24f726110167032159a ))

- save team state | ([02a7c86](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/02a7c8663d04bb6722172b261af62ad658cd87df ))

- eternal id outsources | ([55ecae2](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/55ecae26d66c98580f50c608fdc59c2a6ff474c6 ))

- close modal ticket create | ([5b87356](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/5b87356ebc928b72d7263deb7d4463c7fe02aa82 ))

- order created by first on top | ([0cd5f57](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/0cd5f573ce445135ecabf28e2fbba65fa176fe9c ))


### 🐛 Fixes
- call magnet link | ([0e7ca47](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/0e7ca475c9a54c96eb9dfe9f5e6051f55c56c24c ))


## [1.42.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.41.5...1.42.0) (2025-04-25)

### ✨ Features
- GTA message | ([c262fc3](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/c262fc3597afcf42c19485e9e199e3727052d85b ))

- outsource ticket header | ([5bc7e74](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/5bc7e7499e2a5c09368e3af069c942f11ff8f034 ))

- action modal front done | ([67448da](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/67448da75af8b318f785167a03314d0d0f49092b ))

- reset password route | ([11f1f31](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/11f1f31d7937739204a044952eae23de6516d86a ))

- markdown actions | ([9c6e845](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/9c6e84525eeb5ffe676d3afe9935cf395ad8c9f1 ))

- modal action front | ([801f47d](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/801f47d83e2b5fc86d7c24d25187ba50855cbc2d ))

- markdown editor | ([dcb4bf8](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/dcb4bf8267a0f3ec58caf2a808e283dbb96fa429 ))

- team names + minor fixes | ([399d4af](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/399d4afc6bcbed8654599ecd2bef8ba10c4e31a1 ))

- Chat fully done + removed link login | ([9f3a385](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/9f3a385c85505a3abbdf9533ac11cb5927afaafb ))

- Chat style bubble and team names | ([74e320e](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/74e320e3885ba22a1abd6d7d69a7559ef7ec14e8 ))

- Global switcher | ([2baf5a3](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/2baf5a3544acd03891ee7c471e75dbcd983e6111 ))

- GTR/GTI | ([9effa63](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/9effa63f4456df550c77f025ddea140f67d7de12 ))

- Team switcher front | ([5a0f3c3](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/5a0f3c3d15673f51d5123c6357d7def87f5abd60 ))

- Ticket detail top page + removed right panel | ([7c3f8a4](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/7c3f8a4e25f0a1893b07679dc2a5c74aae12961b ))

- New tickets list table/view | ([3a3a037](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/3a3a03730a703588c8ce00eb34808b033cb3fa64 ))


## [1.41.5](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.41.4...1.41.5) (2025-04-24)

### 🐛 Fixes
- retirer le bouton de création de compte | ([fea60f2](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/fea60f2358834113ccd524fcdb4845f0b9f880df ))
 | ([PRO-1421](https://app.clickup.com/t/42603198/PRO-1421))

## [1.41.1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.41.0...1.41.1) (2025-04-09)

### 🐛 Fixes
- Team - Création d'une team | ([27bf158](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/27bf1586666bde0c318878681e331ae8106b63a2 ))
 | ([PRO-1395](https://app.clickup.com/t/42603198/PRO-1395))

## [1.41.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.40.2...1.41.0) (2025-04-09)

### ✨ Features
- Création de ticket SAV - Ajout de la sidebar | ([faf9768](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/faf976811ded1de5c82637efd7012773f90b0bdd ))
 | ([PRO-1371](https://app.clickup.com/t/42603198/PRO-1371))
- Création de ticket SAV - Ajout de la sidebar | ([2fd24f9](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/2fd24f989d4103994f836b33c1a2ecfe44062b33 ))
 | ([PRO-1371](https://app.clickup.com/t/42603198/PRO-1371))

## [1.40.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.39.1...1.40.0) (2025-03-31)

### ✨ Features
- Récupérer le nom de la PTO | ([bbfe8da](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/bbfe8da00ee41e752aff2ff08c7d21fe379b4f7b ))
 | ([PRO-1306](https://app.clickup.com/t/42603198/PRO-1306))
- Récupérer le nom de la PTO | ([1884bea](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/1884bea4f844e802d5fe6260a7700bddceb22b37 ))
 | ([PRO-1306](https://app.clickup.com/t/42603198/PRO-1306))

## [1.39.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.38.1...1.39.0) (2025-03-27)

### ✨ Features
- Création de ticket SAV - Traduction des valeurs que l'on... | ([3c90296](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/3c9029653996ef13e81366d62c675b17a097c005 ))
 | ([PRO-1346](https://app.clickup.com/t/42603198/PRO-1346))
- Création de ticket SAV - Traduction des valeurs que l'on... | ([17358cd](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/17358cd70aba8c66b9b20f20b52cc6a40594e5c8 ))
 | ([PRO-1346](https://app.clickup.com/t/42603198/PRO-1346))

## [1.38.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.37.0...1.38.0) (2025-03-27)

### ✨ Features
- remove old fiber tickets components | ([8eba79e](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/8eba79ecfe59a8592f505ac574d68ac8ae2105de ))


## [1.37.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.36.0...1.37.0) (2025-03-27)

### ✨ Features
- remove old fiber tickets components | ([af1822a](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/af1822a33462195e9ee65158a24853e828992f12 ))


## [1.36.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.35.2...1.36.0) (2025-03-27)

### ✨ Features
- remove old fiber tickets components | ([b807461](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/b80746129641e428041dbf515e8708c94bc655b0 ))


## [1.35.2](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.35.1...1.35.2) (2025-03-27)

### 🐛 Fixes
- Informations copiées sur la fiche de commande | ([3750624](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/37506243cba214a92f1f4a078a4ad6c4ba6184a4 ))
 | ([PRO-1305](https://app.clickup.com/t/42603198/PRO-1305))

## [1.35.1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.35.0...1.35.1) (2025-03-27)

### 🐛 Fixes
- Intégration de la navbar (masquer certains éléments) | ([52da669](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/52da669f38c7d607be53afe1dd7643284c5bf954 ))
 | ([PRO-1192](https://app.clickup.com/t/42603198/PRO-1192))

## [1.35.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.34.3...1.35.0) (2025-03-26)

### ✨ Features
- Création d'un ticket SAV | ([acee749](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/acee749b221a8a0ac86a69427204fbeaea4fc377 ))
 | ([PRO-1310](https://app.clickup.com/t/42603198/PRO-1310))
- Création d'un ticket SAV | ([70fc758](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/70fc7589226e2935e0c837acb26557baeebde5b6 ))
 | ([PRO-1310](https://app.clickup.com/t/42603198/PRO-1310))

## [1.34.1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.34.0...1.34.1) (2025-03-24)

### 🐛 Fixes
- Déconnexion Pro Ui | ([25d6ebd](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/25d6ebddd64aa3b1cecd445a721121a5cb04eb8d ))
 | ([PRO-1345](https://app.clickup.com/t/42603198/PRO-1345))
- Déconnexion Pro Ui | ([cd9ed01](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/cd9ed018230d76278bc5e833fa013dfb6ce2fbe7 ))
 | ([PRO-1345](https://app.clickup.com/t/42603198/PRO-1345))

## [1.34.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.33.3...1.34.0) (2025-03-21)

### ✨ Features
- Création d'un ticket SAV | ([904ae23](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/904ae2306dfd1baba30d6d515d2f8708692c652c ))
 | ([PRO-1310](https://app.clickup.com/t/42603198/PRO-1310))
- Création d'un ticket SAV | ([5af4826](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/5af4826edb90fcbe92afcf5951cd069b18d782b8 ))
 | ([PRO-1310](https://app.clickup.com/t/42603198/PRO-1310))

## [1.33.3](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.33.2...1.33.3) (2025-03-13)

### 🐛 Fixes
- rework all pages | ([0b65663](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/0b65663354c478d3306d17b1601e89b3f3e04e45 ))


## [1.33.2](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.33.1...1.33.2) (2025-03-13)

### 🐛 Fixes
- implement appointments route in sidebar | ([d0a8e17](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/d0a8e17882ac3361fb3a6e79055cb66ee127d750 ))


## [1.33.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.32.0...1.33.0) (2025-03-13)

### ✨ Features
- rework all pages | ([3a8f800](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/3a8f800f5bebb96069fee760e28ebe43cb97d595 ))


## [1.31.1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.31.0...1.31.1) (2025-03-12)

### 🐛 Fixes
- route treeGen | ([f265b33](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/f265b3305496ecd05659bfc53663768c450d2edc ))


## [1.31.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.30.4...1.31.0) (2025-03-12)

### ✨ Features
- rework the sidebar data | ([6828940](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/68289404ca21dd7d8d0bf09b157fb25f31def0fc ))


## [1.30.2](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.30.1...1.30.2) (2025-03-10)

### 🐛 Fixes
- Information utilisateur | ([a32b2c1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/a32b2c1868c37ab9161dfa810b00bb2a6c3aa8e2 ))
 | ([PRO-1308](https://app.clickup.com/t/42603198/PRO-1308))
- Information utilisateur | ([2134030](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/21340304be270f80a16d86c7c0fd2c80358e655d ))
 | ([PRO-1308](https://app.clickup.com/t/42603198/PRO-1308))

## [1.30.1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.30.0...1.30.1) (2025-03-10)

### 🐛 Fixes
- hide page on navbar and add accessorkey on columns ticketing | ([064183c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/064183c4f0215feb4299a7bfba59feba82a8faae ))
 | ([PRO-1304](https://app.clickup.com/t/42603198/PRO-1304))
- hide page on navbar and add accessorkey on columns ticketing | ([072f6ae](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/072f6ae5c7ba0c54a4b471a0c016336bb2d5c763 ))
 | ([PRO-1304](https://app.clickup.com/t/42603198/PRO-1304))

## [1.30.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.29.1...1.30.0) (2025-03-05)

### ✨ Features
- Login page + spline | ([99e8d7d](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/99e8d7d5f9313290b89edfd2ebdffb08587b1bf2 ))
 | ([PRO-1299](https://app.clickup.com/t/42603198/PRO-1299))
- Login page + spline | ([8325a77](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/8325a77b055a0a3d883a43c30c5ca6e7cff38c43 ))
 | ([PRO-1299](https://app.clickup.com/t/42603198/PRO-1299))

## [1.29.1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.29.0...1.29.1) (2025-03-05)

### 🐛 Fixes
- fix refresh_token format | ([4d1e65c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/4d1e65c6a3f6e2cc30cb2067c1872feb70eb3758 ))
 | ([PRO-1302](https://app.clickup.com/t/42603198/PRO-1302))
- clear | ([2971570](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/2971570c046242b032738a9d250c6af95757df40 ))
 | ([PRO-1302](https://app.clickup.com/t/42603198/PRO-1302))
- fix refresh_token format | ([209221a](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/209221ac3d504892dad0656a2b35ff07a4f6d17f ))
 | ([PRO-1302](https://app.clickup.com/t/42603198/PRO-1302))

## [1.26.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.25.0...1.26.0) (2025-02-26)

### ✨ Features
- table DnD | ([f3da37e](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/f3da37e91ddd265c240a0374a15e314b00c55d5d ))
 | ([PRO-1196](https://app.clickup.com/t/42603198/PRO-1196))
- table DnD | ([9e820bc](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/9e820bce65b8bb814a52a95beb6af992f2ba0e94 ))
 | ([PRO-1196](https://app.clickup.com/t/42603198/PRO-1196))

## [1.25.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.24.2...1.25.0) (2025-02-24)

### ✨ Features
- One filter tickets | ([481c53b](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/481c53befcf35298b34f2542c7c9a9c24572312c ))
 | ([PRO-1246](https://app.clickup.com/t/42603198/PRO-1246))
- One filter tickets | ([61b4db2](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/61b4db298201dbc98060b2063f89eb6fcc8ca398 ))
 | ([PRO-1246](https://app.clickup.com/t/42603198/PRO-1246))

## [1.24.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.23.4...1.24.0) (2025-02-18)

### ✨ Features
- implement new design for admin tabs | ([1971e23](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/1971e23c47e2f777d0746a2f110e26a7f7ed1b2e ))


## [1.23.2](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.23.1...1.23.2) (2025-02-13)

### 🐛 Fixes
- all admin pages & start removing old components | ([27526b2](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/27526b227a4ab686a24155a876d668d95d48a629 ))


## [1.23.1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.23.0...1.23.1) (2025-02-13)

### 🐛 Fixes
- authentication flow | ([323197f](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/323197f83558c74dc94c6b352800a6cb2882aea8 ))


## [1.23.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.22.3...1.23.0) (2025-02-13)

### ✨ Features
- implement a custom-page-header component | ([0b3aa78](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/0b3aa787d500915e38f442ce38621b17cd2cd920 ))


## [1.19.1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.19.0...1.19.1) (2025-02-10)

### 🐛 Fixes
- js memory warning | ([3779c5c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/3779c5cc61875e853539ceea03a6acf493c1033e ))


## [1.18.5](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.18.4...1.18.5) (2025-02-10)

### 🐛 Fixes
- can't reach loading file | ([6341263](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/63412630655c0d49165616a3a9402ad10cf6523a ))


## [1.18.4](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.18.3...1.18.4) (2025-02-10)

### 🐛 Fixes
- deploy | ([e56a3b5](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/e56a3b596693da515f1a600821b24b22d05de7b1 ))


## [1.18.3](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.18.2...1.18.3) (2025-02-10)

### 🐛 Fixes
- vscode config | ([df9da8d](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/df9da8d719ecf24c570c7285fa963a06f0a1cea9 ))


## [1.18.1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.18.0...1.18.1) (2025-02-10)

### 🐛 Fixes
- deploy | ([a72b10c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/a72b10cc76bfa9a050ba504f5edfd167b8a888b8 ))


## [1.18.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.17.0...1.18.0) (2025-02-10)

### 🐛 Fixes
- dockerfile | ([a3bbef9](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/a3bbef9c60469f3753be3e5e60052f324ea633ad ))


## [1.17.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.16.0...1.17.0) (2025-02-10)

### ✨ Features
- filter date + responsive | ([ee4b6d1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ee4b6d146af4ca0a9b0fcf275e7ff04df6c6574d ))
 | ([PRO-1241](https://app.clickup.com/t/42603198/PRO-1241))
- filter date + responsive | ([802c1c0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/802c1c001eb6ae6401aad456a902f255d3a0dbb7 ))
 | ([PRO-1241](https://app.clickup.com/t/42603198/PRO-1241))
- migrate tech folder to universe | ([81b2437](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/81b2437ffbf56f3f6892c5e8cc06bcd2b3d8295a ))


### 🐛 Fixes
- export router into a service | ([d7ca59f](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/d7ca59fdd0e94a3f07b5a78b982d53f55a74e87d ))

- remove unused pages | ([740114b](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/740114b14816b7e7f59f3844e4d300a744c1c667 ))

- **appV2**: init appV2 tanstack | ([70852c4](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/70852c4b702d74773c9fe4a47721156fec52fbc8 ))


## [1.16.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.15.0...1.16.0) (2025-01-15)

### 🐛 Fixes
- **appv2**: init migration appv2 tanstack | ([4b8c887](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/4b8c8877f6e4aceb1ed61cf39659466d0daf31fd ))


## [1.13.3](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.13.2...1.13.3) (2024-08-05)

### 🐛 Fixes
- **ticket - appointment**: resolve view debrief pdf | ([f245e2c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/f245e2c7eac28899479ab93c1d00760bc5815f6c ))


## [1.13.2](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.13.1...1.13.2) (2024-07-29)

### 🐛 Fixes
- **ticket - order**: replace data mock part data api | ([9968498](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/99684984c87922cf642d7829eaa85416f14bc991 ))
 | ([FIB-579](https://app.clickup.com/t/42603198/FIB-579))

## [1.13.1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.13.0...1.13.1) (2024-07-29)

### 🐛 Fixes
- **order**: fixe crash at click map of at map longitude and latitude is undefined. | ([b4dc3bf](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/b4dc3bffb647db850ff322a90100b7e6e9d9b86b ))


## [1.12.23](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.22...1.12.23) (2024-07-19)

### 🐛 Fixes
- OnuDetails | ([ce7fed8](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ce7fed874652e03dd4f7a703cc29e6dd2d3a78a9 ))
 | ([FIB-1018](https://app.clickup.com/t/42603198/FIB-1018), [FIB-532](https://app.clickup.com/t/42603198/FIB-532))

## [1.12.22](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.21...1.12.22) (2024-07-18)

### 🐛 Fixes
- update version sentry cli | ([8396271](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/83962710673fb007c3ebbc50f7dea3abdd743a2c ))


## [1.12.20](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.19...1.12.20) (2024-07-18)

### 🐛 Fixes
- resolve view Serial ONU | ([6dbea1e](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/6dbea1e7df7d5902bdc81651768a7985dd493119 ))

- update graph onu | ([04379e1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/04379e144eb0d7fa8a89d9ab8f070debfbb9710f ))
 | ([FIB-1018](https://app.clickup.com/t/42603198/FIB-1018))
- update info onu in ONUDetails | ([a01ee2e](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/a01ee2efb7a0d8f708dc168d8676552644db0f1b ))
 | ([FIB-532](https://app.clickup.com/t/42603198/FIB-532))
- update version sentry cli | ([80e3049](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/80e30497763f066ff2a491152d5435cf9280c54d ))

- resolve view Serial ONU | ([b071bb3](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/b071bb3471af5b620bf2d49f61b34a14aae534fc ))


## [1.12.19](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.18...1.12.19) (2024-07-15)

### 🐛 Fixes
- **onu**: update telemetry chart in onu details | ([2723f80](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/2723f802ac1cdee3d4f68f0c9e7eef8b629d8b7d ))
 | ([FIB-1019](https://app.clickup.com/t/42603198/FIB-1019))
- **onu**: update telemetry chart in onu details | ([f34a4ba](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/f34a4ba85a6aec43e3f6b9766b755e14314acb2e ))
 | ([FIB-1019](https://app.clickup.com/t/42603198/FIB-1019))

## [1.12.18](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.17...1.12.18) (2024-07-08)

### 🐛 Fixes
- Update .gitlab-ci.yml file | ([f47ef8c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/f47ef8c7a4b0034623c921338499f3151aadc1c0 ))

- Update .gitlab-ci.yml file | ([b3e91dd](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/b3e91dd1e8d698e523a6a63d4ef1739f45f3e6d3 ))

- update config gitlab ci | ([e9c7cd5](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/e9c7cd5ca201c7cfdb820d9d206717e6b24675b0 ))

- update  view and schema for create ticket | ([b1c9070](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/b1c9070c70524fd1e93734ad05fb585406d7cfb3 ))


## [1.12.17](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.16...1.12.17) (2024-07-08)

### 🐛 Fixes
- add ping onue - Tester le ping - CTA | ([a07a804](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/a07a804e423fce659668509aa8a6a5499db2bbe6 ))
 | ([FIB-1021](https://app.clickup.com/t/42603198/FIB-1021))

## [1.12.16](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.15...1.12.16) (2024-07-08)

### 🐛 Fixes
- resolve error view for date in AppointmentItem | ([14f49ec](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/14f49ec41e626fe28fbd82d685568ae57297bb2d ))


## [1.12.15](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.14...1.12.15) (2024-07-05)

### 🐛 Fixes
- update code for recover report appointment appointmentStateTag | ([d48f077](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/d48f0775f8388da3346d0af39c8e2b4004e91305 ))


## [1.12.14](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.13...1.12.14) (2024-07-05)

### 🐛 Fixes
- update code for recover report appointment appointmentStateTag | ([46538dc](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/46538dc00e07c41dab01849f02f723a6e82aeba0 ))


## [1.12.13](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.12...1.12.13) (2024-07-04)

### 🐛 Fixes
- update and refactor tickets page and component FIB-489 | ([31fd3ca](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/31fd3cad4f31637c1165f61633bcf3b4ebd187c3 ))

- update and refactor tickets page and component FIB-489 | ([8792618](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/87926180baa3fa201e02fe09f72b1a54bb6d3f2f ))


## [1.12.12](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.11...1.12.12) (2024-07-02)

### 🐛 Fixes
- update package reactor-icons | ([da16381](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/da163817878a07df0c872bcf5d06002923f4bb1b ))

- update package reactor-icons | ([ea8c072](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ea8c072b66565443f21eeb8f251e42b2e58d1b2b ))


## [1.12.11](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.10...1.12.11) (2024-07-01)

### 🐛 Fixes
- refactor, lint and clear code review ticket page and create tickets, TO... | ([fa83cd7](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/fa83cd7c5e44c8837e210e12c886351ed3ea690e ))

- update package and add toaste part admin gestion | ([1644223](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/16442231581634c47a3ccf37889f2cfef17a4076 ))

- refactor, lint and clear code review ticket page and create tickets, TO REVIEW create ticket (space, products space) | ([d28338c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/d28338cde1b53a8fd02e3809d7261699ae74b5b0 ))


## [1.12.10](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.9...1.12.10) (2024-06-26)

### 🐛 Fixes
- resolve form view and merge conflict | ([a157c2c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/a157c2c9705c6674c85c9af647117b239837a6d3 ))


## [1.12.9](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.8...1.12.9) (2024-06-20)

### 🐛 Fixes
- **sentry**: Fixes SENTRY-317 | ([ced617b](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ced617b059e17bd0ddae08e90ac121f18e818d45 ))


## [1.12.8](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.7...1.12.8) (2024-06-19)

### 🐛 Fixes
- resolve path font ibm_plex_mono not found | ([62a90c4](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/62a90c45363afff776aaca1b337a88b2f5eb7fb5 ))


## [1.12.7](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.6...1.12.7) (2024-06-19)

### 🐛 Fixes
- FIB-943 - Rendez-vous : Plusieurs appointmentSchedule | ([ce97966](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ce979667e1c83301110ef5a20c7963d7cf807e1a ))


## [1.12.5](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.4...1.12.5) (2024-06-18)

### 🐛 Fixes
- update config sentry add sentry cli | ([48b1f03](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/48b1f032fa48ce8d77903e5b5afa2e829b3107a7 ))

- update config sentry | ([2374c90](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/2374c90b9e4c457492abc723516ca422914690d8 ))

- resolve crash order page and fix view workflow | ([bed5ea5](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/bed5ea545f4c613d3a879e31680e9ca305adc1dd ))

- update config sentry add stack tag | ([344843e](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/344843edaedc0c311b23f982e6e56e13b6d6f1e1 ))


## [1.12.4](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.3...1.12.4) (2024-04-26)

### 🐛 Fixes
- lint ts and eslint | ([9934593](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/99345938a01c4530d57d79073188772ddc8cdd89 ))


## [1.12.3](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.2...1.12.3) (2024-04-24)

### 🐛 Fixes
- resolve edit staff | ([d7104ac](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/d7104ac5f230ce46c5ed33b814ed1915495747c7 ))


## [1.12.2](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.1...1.12.2) (2024-04-24)

### 🐛 Fixes
- error ci | ([c41a364](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/c41a36454b8214d0218404d1ac08c0f84a91c6de ))


## [1.12.1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.12.0...1.12.1) (2024-04-24)

### 🐛 Fixes
- disabled ping onu add log.erreur patch staff | ([ba8e473](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ba8e473db9a85f63ec278769c1beaba266c9f9e0 ))


## [1.12.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.41...1.12.0) (2024-04-16)

### ✨ Features
- add feat first version ping onu | ([b73b44d](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/b73b44deda0632864acd377224b883341e0837d2 ))


## [1.11.41](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.40...1.11.41) (2024-04-16)

### 🐛 Fixes
-  update wording in fiber order communication section | ([ac693c3](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ac693c37fa5757e750c5c6c547e0d2d7ef1db920 ))


## [1.11.40](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.39...1.11.40) (2024-04-08)

### 🐛 Fixes
- fix display document appointment | ([a1e8a31](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/a1e8a31b4a45679908bad9724aa22a4d2e51bfdd ))


## [1.11.39](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.38...1.11.39) (2024-03-28)

### 🐛 Fixes
- fix create ticket and update view ticket for add delete is creatore of ticket | ([4b1f53c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/4b1f53c92276b5b093220cec58350def8e42ee16 ))


## [1.11.38](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.37...1.11.38) (2024-03-24)

### 🐛 Fixes
- fix create ticket and clean,refactor part ticket | ([c976e6b](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/c976e6b1bc23e28929c05550e759eceb9893f641 ))


## [1.11.37](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.36...1.11.37) (2024-03-15)

### 🐛 Fixes
- update ticket | ([6973d60](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/6973d60b08b4d7f9135a06914791bcf0ad2b74df ))


## [1.11.36](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.35...1.11.36) (2024-03-15)

### 🐛 Fixes
- clean query ticket | ([383eb3b](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/383eb3b4d45b4a74994231f4d98e88b751ac980a ))


## [1.11.35](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.34...1.11.35) (2024-03-15)

### 🐛 Fixes
- update create ticket | ([7fc9eaf](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/7fc9eafbcf6094a0a53b676c10f8cc6e2306724c ))


## [1.11.34](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.33...1.11.34) (2024-03-14)

### 🐛 Fixes
- update create ticket | ([1973af7](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/1973af72d951962cea8cfce63e5807735b02b4a8 ))


## [1.11.33](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.32...1.11.33) (2024-03-13)

### 🐛 Fixes
- update creation ticket page and ticket page | ([bb79f4c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/bb79f4c383ddb6d71733473045c7c9cd4e3afbb7 ))


## [1.11.32](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.31...1.11.32) (2024-03-12)

### 🐛 Fixes
- update minor | ([a2c88cc](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/a2c88ccc20eaebd6d806a3cc58b17201c3be5a62 ))


## [1.11.31](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.30...1.11.31) (2024-03-12)

### 🐛 Fixes
- ci job package | ([875d865](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/875d865c6d876956f3d72919c9cd5fe729df80b5 ))


## [1.11.30](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.29...1.11.30) (2024-03-12)

### 🐛 Fixes
- update creation ticket page and ticket page | ([9104869](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/91048696a3134799b813e9d0b59440534638d459 ))


## [1.11.29](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.28...1.11.29) (2024-03-11)

### 🚑 Bug Hot Fixes 
- resolve system connexion | ([1441b46](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/1441b46c857fc8253875de6e01cbd833a9573f91 ))


## [1.11.28](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.27...1.11.28) (2024-03-11)

### 🐛 Fixes
- update code for part ticketing | ([271de9a](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/271de9adfbc9c64ef6a2975da99a1532224efe76 ))


## [1.11.26](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.25...1.11.26) (2024-03-07)

### 🐛 Fixes
- link invoice pdf and gross refactor ticket | ([4ad99a4](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/4ad99a45b526d9f08c050913c860ff47f05759e7 ))


## [1.11.25](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.24...1.11.25) (2024-03-01)

### 🐛 Fixes
- update wording head company info | ([cf30b46](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/cf30b460c3fb6bdc9e05cc5958a60ce4edc0813c ))


## [1.11.24](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.23...1.11.24) (2024-02-21)

### 🐛 Fixes
- **order**: resolve bug ci job package | ([79148ff](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/79148ff1260c7060d0d93e339e23ceeea7596c21 ))


## [1.11.23](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.22...1.11.23) (2024-02-21)

### 🐛 Fixes
- **order**: update info wholesaleReference and part magnet ticket | ([b503186](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/b503186f70fe6c3a647008862544145e674e7ebd ))


## [1.11.22](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.21...1.11.22) (2024-02-14)

### 🐛 Fixes
- update config dockerFile | ([87077a0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/87077a06e171bacf2fb034d89574174869e50b94 ))


## [1.11.21](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.20...1.11.21) (2024-02-14)

### 🐛 Fixes
- **order**: add wholesale_reference to info company | ([4d215f0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/4d215f03d0fe4fe57a28c0040cd052acb8037f4f ))


## [1.11.20](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.19...1.11.20) (2024-02-08)

### 🐛 Fixes
- update config pwa and add page offline | ([5de23fe](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/5de23fe75849f7dc8c30d48e5c7855a8bab1ae98 ))


## [1.11.19](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.18...1.11.19) (2024-02-07)

### 🐛 Fixes
- update config pwa test | ([91f6bcc](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/91f6bcc326d701a832db9c97102124472702977a ))


## [1.11.18](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.17...1.11.18) (2024-02-02)

### 🐛 Fixes
- update gestion admin | ([e1b2d0c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/e1b2d0c6c63ad3b7592759563428d4faeaaec3eb ))


## [1.11.17](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.16...1.11.17) (2024-02-02)

### 🐛 Fixes
- update gestion admin | ([b237f29](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/b237f29b253ac99aa7ac9b730b4cdd8087d91d7f ))


## [1.11.16](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.15...1.11.16) (2024-02-01)

### 🐛 Fixes
- update gestion staff | ([ae50eba](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ae50eba8d028bdc4511a25c3de91359bd409ecff ))


## [1.11.15](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.14...1.11.15) (2024-02-01)

### 🐛 Fixes
- update access role for admin gestion page | ([7c91e6b](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/7c91e6bdf70c91bcfeadfd0829cd4ed3f5dbc706 ))


## [1.11.14](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.13...1.11.14) (2024-01-31)

### 🐛 Fixes
- update role staff for gestion admin and set-password | ([c295073](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/c2950736bc745841bac5c86e943b60af516e52b0 ))


## [1.11.13](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.12...1.11.13) (2024-01-30)

### 🐛 Fixes
- update role staff for gestion admin | ([aa5e36c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/aa5e36cb9057f7a1c214fe23428487297ad271f3 ))


## [1.11.12](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.11...1.11.12) (2024-01-30)

### 🐛 Fixes
- ci package job faild | ([621d90e](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/621d90e1070b23d25b892b59db078ad3b60952cf ))


## [1.11.11](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.10...1.11.11) (2024-01-30)

### 🐛 Fixes
- update gestion admin | ([80f8fd7](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/80f8fd78532c78f1e061c6941c82191f276f6fec ))


## [1.11.10](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.9...1.11.10) (2024-01-30)

### 🐛 Fixes
- update gestion admin | ([08f9ac1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/08f9ac17ed781a077caecfac20ddfba6b082835a ))


## [1.11.9](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.8...1.11.9) (2024-01-29)

### 🐛 Fixes
- update gestion admin and set, rest password | ([f66f39c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/f66f39cbcbb27061cc7616f29dadd2ed9ac9bf90 ))


## [1.11.8](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.7...1.11.8) (2024-01-24)

### 🐛 Fixes
- update list linkConatc | ([582d4d1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/582d4d1ca8165c57b061038f39eec2186b2e30e4 ))


## [1.11.7](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.6...1.11.7) (2024-01-19)

### 🐛 Fixes
- update access admin page creat staff and config sentry | ([053cf1f](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/053cf1f5c79999fc86d16b216f2f7b5169f56314 ))


## [1.11.6](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.5...1.11.6) (2024-01-16)

### 🐛 Fixes
- update access admin page and add champ number contact for create staff and edit staff | ([c1c00fa](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/c1c00fa4ecd290de67c9fc3f38142ef3182b87a7 ))


## [1.11.5](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.4...1.11.5) (2024-01-15)

### 🐛 Fixes
- update package and bug of reactore | ([c693fd6](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/c693fd63253a84f84a35e7a535c8bb8473c4fa1a ))


## [1.11.4](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.3...1.11.4) (2024-01-15)

### 🐛 Fixes
- resolve failed job package | ([7eb64d8](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/7eb64d8b9dba64f4c68c28c585d7685eb62b82c5 ))


## [1.11.3](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.2...1.11.3) (2024-01-14)

### 🐛 Fixes
- resolve failed crash page | ([b3ad5cc](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/b3ad5cca8eb048363291ba4c5e570a2fd6ad50c3 ))


## [1.11.2](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/1.11.1...1.11.2) (2024-01-14)

### 🐛 Fixes
- resolve failed package job | ([3697fad](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/3697fadfd70fdfc1b135dcdd7c6efe9192442a5c ))


## [1.11.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/v1.9.14...1.11.0) (2024-01-14)

### MAJOR
- **structure**: restructure project client -> app | ([65bf703](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/65bf7035f23838e51f6942e52a9a1b53f2eb2e85 ))


### ✨ Features
- **staff**: copy staff page from audeit | ([c681346](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/c6813461c5b24d070d63c22d9cc89f4a46fb04cf ))

- **common**: add translation for network and oi states | ([77ba771](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/77ba7719d83ebd0c63b91c14e6c4d2604a7270c0 ))


### 🐛 Fixes
- update ci job otify | ([2f0ae49](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/2f0ae49e79a6e8276e7e01f806ca637aa2a3f269 ))

- restructure and uniformise as iron | ([f6b3878](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/f6b387809a28172473dede9cfe06b237df49b290 ))

- **order**: use offerorder from useorder and filter by deleted at for fixes | ([3dddacc](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/3dddacca85cda87582a65f275e262ef033321257 ))

- **order**: use offerorder from useorder and filter by deleted at for fixes | ([99bed2e](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/99bed2e947cbfe6578d42d0c2cfab781257a6da0 ))

- **useauthworker**: add is expired to logout if token expired | ([d851ea4](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/d851ea4acba22028714347ed7d5f69d4cf1eda24 ))

- update state in histories | ([68e006e](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/68e006e1081f4c251992166284fef14c6a58dc70 ))

- **refreshToken**:  test fix refresh_token and add feat minor | ([67ecf45](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/67ecf45f323026544548edb5b4add07e4bf5207b ))

- **refreshToken**:  test fix refresh_token | ([192b924](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/192b924d642af00635bca2d098d1b2da5a900650 ))

- **refreshToken**:  test fix refresh_token | ([06a7a88](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/06a7a884b109cfeb0f09b144d85db0c2413bc0c8 ))


## [v1.9.2](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/v1.9.1...v1.9.2) (2023-11-10)

### 🐛 Fixes
- **sentry**: update package and config sentry | ([dfe71df](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/dfe71df77f4ec0abedcbb081dbe71c02e207b7f6 ))


## [v1.8.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/v1.7.1...v1.8.0) (2023-10-13)

### ✨ Features
- **departmentfilterbutton**: build department filter drawer with expander | ([0652c00](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/0652c004e26b265f67bcaf2d16de0302f8941817 ))

- **drawer**: build drawer for filters | ([0bc0a53](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/0bc0a53df8e4a2ee103f77808ff3c2dd835bf7cc ))

- **drawer**: init drawer component | ([bd39b22](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/bd39b22a31e3780c441415fac5b7625ece2963ec ))

- **ticketskanban**: add filters btns | ([c8ba5bd](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/c8ba5bd0ff5945ed64ae88b481744d44f2ccaf43 ))


### 🐛 Fixes
- **order**: correct link of previous page | ([222c083](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/222c083a634ee96aba7c17dbbe5c3c2962ec08bb ))

- **ticket**: action ticket in chat | ([a7d1a7a](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/a7d1a7a2c0f30e64b5818e5f0b11b2ebf82f5905 ))

- **tickets**: done create ticket and liste tickets | ([1fe894d](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/1fe894d87c1aa0333818cc07baa46433dbea5df8 ))

- **review**: review fiber order and magnet tickets | ([4d39c13](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/4d39c136a737faf5697354e476ee35cd3d7aa574 ))

- **order**: resolve crash view | ([40026fb](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/40026fb069e98bb9a0a0429b76a6484c82913a68 ))

- **order**: resolve crash view | ([aa04da6](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/aa04da62bf397dad07186d7b6bdead9e2df51499 ))

- **order**: resolve crash view | ([413d109](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/413d1098ee4921cfc001ddb728029ee375a58b94 ))

- **order**: resolve crash view | ([de32e1e](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/de32e1e7dd2a238ee1e87e80105b027307bf3d4e ))

- **order**: resolve crash view | ([8e4e161](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/8e4e1617b7429853652c24e9f6be5e2faf1cb09e ))

- **order**: resolve collect details crash view | ([7383f03](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/7383f03c2ec8573f8399f19212973641d63bfa53 ))


## [v1.5.2](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/v1.5.1...v1.5.2) (2023-08-23)

### ✨ Features
- **otherticketitem**: display other ticket details | ([9078072](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/90780722e9feae90cbf786947945a3492987428b ))

- **othertickets**: build other tickets tab | ([d9ff9a0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/d9ff9a0af2cdf6cfd4462197da6bcfc2e890d939 ))

- **ticketappointments**: build appointments tab | ([ba80c66](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ba80c6662aee44903e8cfcf6d1d801fcee63ccd6 ))

- **tickettypology**: add typology tab | ([73cfbab](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/73cfbab68e2982852f99efb74911e810841c8cc8 ))

- **ticketorder**: build appointment section and footer | ([07e1253](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/07e12531991999c6284f49f1db016a88dc8fb00b ))

- **ticketorder**: build ois infos section | ([11364f4](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/11364f4d0401a280d4e6d35b235f287a3707a884 ))

- **ticketorder**: build vlan and order infos sections | ([795aa63](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/795aa63c8c360237c433502425f112bdbce3bc00 ))

- **ticketorder**: init order tab | ([a2cd76e](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/a2cd76e7c68ba2d5e3a50e217424e12ccf84565e ))

- **ticketsidebar**: init sidebar with icons | ([b1eec3b](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/b1eec3bb52a3fc21ff741206675e50de95be2656 ))

- **ticketdetailsheader**: add gtr and workflow section | ([244d906](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/244d9068ed75102333d1a30c002ff9243ad980cc ))

- **ticketdetailsmessages**: group messages and event items for conditional display | ([2dad1d0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/2dad1d0e49d0d7bffb7c34f653962a6c52506397 ))

- **events**: add mocks | ([d2c7775](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/d2c7775ace1052d0d8d6e8a9542fe766a3db2279 ))

- **updateorderparams**: put select option for acquisition and sla | ([21fac53](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/21fac538503ceaccdc007df41bbc51851b9e2ecd ))

- **updateorderparamsmodal**: link inputs to useForm | ([ff00c6d](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ff00c6db2d12c5221193054a36fd34e95210d07a ))

- **updateorderparamsmodal**: build form for put order | ([3365ebe](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/3365ebe6dc095f55e5d58d547492420bcf6aa072 ))

- **orders**: keep last search in search bar | ([60cae03](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/60cae03b06001fc0bf97a796af89c5f8b0091dff ))

- **timeline**: add 1st ping data and update state colors | ([cab2a51](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/cab2a518a1b01e54cf8c1f89152f74357ac7fdf8 ))

- display details of events with modal if long | ([ab53572](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ab535729351e75b446f48de344d5afee8ea66ff3 ))

- **staffs**: add handleclose and use api errors | ([d0dae4e](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/d0dae4e6f5df9aa3ea98188d1d7fcd0380a35202 ))

- **staffs**: set base for crud mutations and forms | ([dd4f2af](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/dd4f2af8fbea269021ca30029f66f88096befb2f ))

- **createstaffmodal**: create modals component, rename to staff and add form validation for post | ([7bcd1cf](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/7bcd1cfbf97d1e05872a668c69e8013a3daa9fbc ))

- **users**: set base for crud pawn users page | ([bea8fee](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/bea8fee6ef7e984a7c405cd44cf10e5e21cf22f9 ))


### 🐛 Fixes
- **ticket**: update api route and interface for magnet | ([c851d47](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/c851d47f63bddffcd8aaa7239a7448943b32713a ))

- **ticket**: update route ticket api | ([9c3bb9b](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/9c3bb9b08228582edbd1034bacac5699f445e649 ))

- **login**: update staffJwt | ([4fc7a78](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/4fc7a78fefb13244804b0360455fddf77efa89bc ))

- **review**: update ui and functionality, appoinment and ticket page | ([334b761](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/334b761c2ebf6a4f8f2968993edeb9b576aaed46 ))

- **review**: update orderFixe | ([63d0d89](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/63d0d89488099115ae3818f227d0d8be0e46f919 ))

- **review**: update orderFixe | ([53a879f](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/53a879f4cd840578bd02096c404bed0f89416251 ))

- **review**: update orderFixe | ([1d7f6c9](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/1d7f6c9136b628b84389df22e6f1f41831dcf9b8 ))

- **navbar**: fix position indicator due to misfunctional scroll listener | ([b645854](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/b645854a04a7a6a5a0571f674864786a36457729 ))

- **workflows**: handle calculation of the width of the lines | ([d85785d](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/d85785d1c43c23650314ec46ea73178301273f53 ))

- **workflows**: handle type errors | ([c059527](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/c0595270d4bf03ae37b68b3290b1ca94acb1cdeb ))

- **timeline**: put back icons for communications | ([a6c81eb](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/a6c81eb5fcb50d2af00211375629aec6a80d0f6b ))

- **review**: review functional ux ui | ([1b5e44c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/1b5e44cb585298b75b75a77f123843ea2d288e42 ))

- **review**: doing review functional | ([41ea077](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/41ea0770b2aab0e51fe8c45919f01c9b219d82c0 ))

- **ci**: resolve bug ci build step | ([eb710d5](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/eb710d54b5262999050eb7c0d905888ca7cb61b5 ))

- **theme**: update and implementation of darkc theme | ([4917d13](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/4917d13e86c3617ff808458627fc19031a9a9e2b ))

- **cell**: resolve of the logout user at search line. | ([6eba6d5](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/6eba6d5a0caf0b1a26375b60be00be4cad17dc18 ))

- **theme**: add custome function atome | ([908fef6](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/908fef6e1d7dc927bc83441fb9bb61e1f08e5d14 ))

- **package**: update package sentry and jotai | ([783f3a1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/783f3a1579dfc3cab5349d633636171becae0fd8 ))

- **tableappointment**: fix table pagination and req params dates | ([f177ff4](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/f177ff449ed659fdc429c59a0e51eb17b4591127 ))

- **sentry**: update captureMessage for fetcher | ([425cb29](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/425cb29a8367af80aaf7a92f84aded966f9645ff ))

- **usemetricsapp**: check if api return an object for metrics app | ([c059451](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/c059451e118bb9380e772d740dd144d2627ffe43 ))

- **staffs**: fix header not sent in post | ([71b7a7c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/71b7a7ce5da19b849be98ef57f26d5a07c0690b8 ))


## [v1.1.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/v1.0.3...v1.1.0) (2023-07-17)

### ✨ Features
- **project**: version deploy and testable | ([7519eb9](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/7519eb946ea31d3f90403b94c0144023457ad353 ))


## [v1.0.0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/compare/...v1.0.0) (2023-07-01)

### ✨ Features
- **deploy**: add and update config for deploy prod and pbe | ([b40dfd2](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/b40dfd2c3dc899d08d85d88a2531e81ec9d3a8cf ))

- **fixes**: display fixes in order | ([02844c0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/02844c04c10b022a968e92492df5f0abb0177b52 ))

- **fiberAndTicket**: update ui order page for fiber and enabled ticket page and resolve error | ([ac16689](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ac16689918114542c1ecee014970bf8d0f8aa869 ))

- **fiberAndTicket**: update ui order page for fiber and enabled ticket page and resolve error | ([d81b0c9](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/d81b0c9d59051b88efc279e930cc39f4c413cb57 ))

- **appointment**: add appointment extra planning panel | ([9b3e53f](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/9b3e53f6b5d9b548bd0b953c777ae24ac8145473 ))

- **appointment**: add post appointment schedule req | ([83c4a12](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/83c4a12d88d237fbb12d4afb9d938d09cc392052 ))

- **appointment**: add post schedule | ([0d62b1e](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/0d62b1e90961fa06285a58d8fdc2a5aafcadc2cb ))

- **appointmentpanel**: add query to get planning | ([5fc847a](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/5fc847a7e6293fd9b4d8d716c6716f7d7be258de ))

- **telemetries**: display data from api | ([fb93e56](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/fb93e563afec262c30769afb24d709e1994a0156 ))

- **project**: first version deploy | ([db5aa00](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/db5aa00c4f8e6b4da1790b9a3115dc9b90ece7a5 ))

- **onudetails**: onudetails | ([5e12b42](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/5e12b427ae5a66eef6186feb1c3d41733d480b52 ))

- **sentry**: add config sentry | ([fcf632b](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/fcf632bed12bc215d1dee8e0bb1c5449382ba116 ))

- **ordermap**: init display map of order endpoint address | ([4c007e6](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/4c007e6323bf277f423bfb004e5c12a060728cb6 ))

- **lineinfoportabilitytabpanel**: 175 format number in portability and copy btns | ([3514c33](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/3514c3321b561d05330e87ef762c669fc8e0b59a ))

- **multisitedeployment**: finish design of section | ([e1b8786](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/e1b8786235f14a506ac3047fcdfea693928fb25b ))

- **companies**: addd page companies and company page | ([8764d38](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/8764d38b8c079aacca1a924095b6e4cb9b6c8877 ))

- **companies**: addd page companies and company page | ([c987feb](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/c987feb91ed555e7e9ea2bf876ebfd8a0cf01f8d ))

- **login**: redirect to dashboard if user is connected | ([c81ba9c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/c81ba9c556d90cd8dd99faef997c6369b0c898ee ))

- **tabs**: allow disabled tab title | ([b76ac05](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/b76ac0552a5487fca17db5c587827005b8e126d9 ))

- **login**: display error on login errors | ([70d46f5](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/70d46f5b1d724f0184094cd87a22414ca1304c22 ))

- **ordersimmodal**: add api requests post order | ([8edb742](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/8edb742125aed54d5ffcd28a0789250becaa7d39 ))

- **clientinformations**: display scoring and contract type | ([6bb1de6](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/6bb1de6f1bf5db957ad58d07ad509d8396b8fc23 ))

- **activatesimmodal**: get inactive sim cards and display in modal with order date | ([7ebcca7](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/7ebcca7086e22e95d071984be318005a079e463e ))

- **porta**: create ask & cancel porta modals | ([b4159be](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/b4159bea3807c267fcc7f36f0256ef7743d9e0ab ))

- **order**: move requests to factory & refetch only active req on refresh | ([3f4aca0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/3f4aca01f97df9caf199c001ea9c935bdef03a24 ))

- **rio**: display rio in modal | ([90d8fd1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/90d8fd1eef8ba26a8c190655ac87d6714484f4ef ))

- **consumptiondetails**: add data & sms mms modals | ([5d2c484](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/5d2c4849006475e035436e36204e626377e2bae4 ))

- **consumptiondetails**: display fr calls details modal | ([bf8e5a1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/bf8e5a15d8fcd7d3d3c252f279b40fa18a462344 ))

- **ftthboard**: redirect to order search page on submit | ([a3229d3](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/a3229d38b6c28e05940ba99a239c49e719a65738 ))

- **orderprogress**: show all events and events states | ([707ee2b](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/707ee2b73fe0866108ee6969d8548fb5333f7c63 ))

- **orderprogress**: show all events and events states | ([7376d6b](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/7376d6b0ca64de12a01f6f51b102e0c376682941 ))

- **typologycables**: display api data for cables | ([76820fd](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/76820fd6f30055d1953f1f27c57805b144b044d6 ))

- **cablesbutton**: display cable buttons in line typology | ([c4104e8](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/c4104e8d71cb3c586959c19c17c34bc0a52f208c ))

- **ptodetails**: create card for pto details informations | ([4603876](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/4603876f9280294b65065b09079ac50a82124361 ))

- **onudetails**: onudetails | ([0a57acf](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/0a57acf2286268f7af2ecb4146303d89c572ff8c ))

- **onudetails**: add chart from json mock data | ([42b9729](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/42b9729ca21e950df6ad1c5bef63053a1389691e ))

- **nrodetails**: display nro infos | ([317e0bd](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/317e0bdd954b00d3eea2af1d4b087be452c12fd7 ))

- **onudetails**: onudetails | ([38c8966](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/38c8966f001822a96ca5e5dc2761b95a8401e383 ))

- **onudetails**: add request for telemetries | ([6b83605](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/6b836057798067b4bcafb636e64e3ac159777967 ))

- **onudetails**: onudetails | ([eb09db0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/eb09db0f67f261ea28d1183ff2707f18c226f640 ))

- **nrodetails**: display nro infos | ([198c762](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/198c76254277c5fb11827895554da1730e000bfb ))

- **orders**: request 500ms after last input change | ([8d80536](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/8d80536602456e79b55ebf567577e39564a0790f ))

- **fiberftthorders**: create table and add query | ([bae9cf3](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/bae9cf3b4f67dd97d1cfc9f4f1be9e57bc3d703e ))

- **fiberftthorders**: move fiber order id to folder and init search order bar | ([d1e8a6c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/d1e8a6cb825c9d1917e05509d54bf9736fd5b3e6 ))

- **updateappointmentmodal**: define form type and handle submit behavior | ([e3ccc70](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/e3ccc7060d627620866cf9ce9da2259c451a071b ))

- **updateappointmentmodal**: add header & footer | ([b1fdf77](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/b1fdf7713a44b966372ba003549de61c291fa941 ))

- **updateappointmentmodal**: create modal & input for form | ([ffaf7f4](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ffaf7f416209535195f5d6a359f4201952133104 ))

- **collectedetails**: add details from api | ([34b853d](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/34b853de34086a79499fce36dcaa5bd299526eef ))

- **collectedetails**: add details from api | ([db026f6](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/db026f69821b58781c629798b0fff8ffcee441bc ))

- **clientinformations**: display api contacts | ([7981a9a](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/7981a9acab5382c46fae3f5b8f643a1461ab361f ))

- **clientoffer**: display name and creation date from api data | ([054fa1c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/054fa1c7995612e71ae7fba0c6f0660c194c8e4f ))

- **fiberftthordersheader**: update order ids datas from api | ([a5d4c4b](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/a5d4c4b90cdd429c17903239be3fd8d2be7d82bd ))

- **logistic**: use api data | ([292f473](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/292f473fc40bc06c1843b3bb6f4efa0b9a10feb5 ))

- **tech/ftth**: first version of board from the page: /tech/ftth/board | ([bbcde90](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/bbcde90f9c5998744ef7056b71a5b8d65115e484 ))

- **order**: first version ftth order and order mobile line | ([59999f3](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/59999f34a6930a3143730505e28a4caedaf5111c ))

- **ftthorders**: build header & line typology sections | ([23680d8](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/23680d8f89c6b9939d03486644e2304242f78291 ))

- **sidebar**: first version functional sideBar | ([fdedd6c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/fdedd6ca4c2c4d9a6f57ffdfa4e2b1b68c0f9cce ))

- **sidebar**: sidebar open in progress | ([3cc2273](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/3cc2273e1545261424ced832d4dc48c2bc255cef ))

- **login**: fiste version login page and update config | ([7a1d9e4](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/7a1d9e40a03e9b53e964fd273d2a0cac510af9b4 ))


### 🐛 Fixes
- **sentry**: update config sentry | ([4eeece7](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/4eeece70a1b3fd31941cf746a05fe919204617a1 ))

- **sentry**: update config sentry | ([6108714](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/6108714ae4f8b2a84331da5af57fdeb09e403282 ))

- **config**: update sentry config and package | ([323b9c1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/323b9c15d93df26ef06bc7c943822dfa5fc9c77e ))

- **orders**: fix typo for orders address type | ([ca18c08](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ca18c088d7ca44021050f1d6059ef5ea3258135e ))

- **orders**: fix typo for orders address type | ([c9d0480](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/c9d04809e4c5961a3044944c9415a2768348c837 ))

- **fiber**: resolve crash page fiber/order | ([63ef406](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/63ef406b1d080740ac03607d969e26792193b561 ))

- **fiber**: resolve crash page fiber/order | ([dfd9b09](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/dfd9b098050d2d604abc2cca832884086b99a475 ))

- **error**: disabled errorBoundary custom | ([54af8e4](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/54af8e43f91c16237e6666c83d6b6978999bf62b ))

- **package**: resolve error for a pnpm install | ([cd130e7](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/cd130e7be5419ec5f1538cf025133975aa042780 ))

- **navbar**: put back scrolling feat | ([dd0dbab](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/dd0dbab0ae7215eae81774070674fdb88b52d0a9 ))

- **sentry**: update config sentry and remove send error sentry if request status code= 401, 403,404 | ([dc5e35e](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/dc5e35eb2b1f4a5b36c03e94b115eee704dcd1c6 ))

- **sentry**: update config sentry and remove basic auth nginx | ([f7ddfda](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/f7ddfdad92bbd5b874fd9e32f74365436c1bd00d ))

- **incidents**: use specific request for incidents | ([f40844b](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/f40844b0c9fc530684aa7471ef20e21f7a176a20 ))

- **ci**: update route relay sentry proxy pass nginx | ([cb80b33](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/cb80b33fd24c0c6097edf38c2266163f70002972 ))

- **sentry**: update route relay | ([c8d2319](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/c8d23199adb9b3714344007df67e62a87935b089 ))

- **connection**: put back login errors messages | ([8ce3a78](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/8ce3a7883c6061ac42058e7833f093ba684d1a33 ))

- **deploy**: fix wording | ([dae1986](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/dae19864c07beae04f647819677daad4f63afe85 ))

- **test**: test deploy | ([8980139](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/898013953ac76b8c8dcb1ea2c084dcd59af3d6a0 ))

- **test**: test deploy | ([ec9cd47](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ec9cd472486ad4aeb7ece6affb59019cf5378de8 ))

- **nginx**: add api routes nginx for pawn and magnet. | ([c63c244](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/c63c24432194b3fa9e28cb4e0afdbcceeb5d41a9 ))

- **ci**: resolving bug to deployment ci of pbe to build step. | ([aadb482](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/aadb48240bf72c03083363123b3c7221e69614f7 ))

- **ci**: update test fix ci | ([144928f](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/144928f03e356f060228dc60acb1b9cfe4dd8178 ))

- **ci**: update test fix ci | ([19c5fd6](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/19c5fd61013214ba438f8406220f68cf95890620 ))

- **ci**: update test fix ci | ([08ef26f](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/08ef26f395270d6b5b61993e1146086721d2ce64 ))

- **ci**: update test fix ci | ([fa0fb29](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/fa0fb29224c70105700eacf11b7843463e740be3 ))

- **sentry**: update config | ([6af650c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/6af650c0400d4f8470893eb0759725674600b6ce ))

- **sentry**: update config sentry for test | ([d286820](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/d286820cec4f60f04f91236c599f01a88f1c30ed ))

- **sentry**: test config sentry | ([f67ca9c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/f67ca9c6032bda4cc444c10729c87c2ce69ba6be ))

- **config**: update config | ([8a47d80](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/8a47d8053cfb4066744b62818c40bb453332cb6d ))

- **sentry**: test config sentry | ([9d6d6c1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/9d6d6c13d1685d4f7e26e051c4a5b953c29e5c3e ))

- **ci**: update config | ([5260a76](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/5260a76afabc5b15652355d92982231cb6c8a1bd ))

- **ci**: update deploy | ([805b0de](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/805b0de289368e8579274b98f4ae93ee4a283739 ))

- **ci**: update deploy | ([5f9f005](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/5f9f005b3dcc781a7d122663206525b44434b50b ))

- **sentry**: update sentry | ([d05fe3d](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/d05fe3d8cec73c3e17f2d028bcc429e17a3c1958 ))

- **sentry**: update ngnix conf | ([ff6f2f1](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ff6f2f13295f9878ccb58f9a07dd2eb022022f82 ))

- **ci**: fix build next | ([574ebba](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/574ebba2eb57656bbaf31386a79572408d9f00d1 ))

- **sidebar**: set is active true as default | ([b4033fa](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/b4033fa16e2e0f3ee2a93ae1d8fb8277a3775269 ))

- **ordersimmodal**: add created by in post order body | ([608b155](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/608b155235dc2e2920e8b710fd4b64dfcf4c2e6b ))

- **input**: remove w-full for parent div | ([8accb0b](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/8accb0b41ca548d2a3ceb3f3e2525a666ef0430a ))

- **sidebar**: reset sidebar atom on logout & update endpoint address interface | ([b0d618b](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/b0d618b72552e6fd8d5b61d2f14ff93fe88c8788 ))

- fix reactore import | ([e065390](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/e0653905b891877ff82ca08737f88c5475b319e4 ))

- **ordertrackingcard**: fix trackings request | ([ee050ee](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ee050ee7b553200dd7ec36127389d306bb95c576 ))

- **logout**: delete prev page if logout | ([23cd66b](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/23cd66b74e51d8a9452cb7180ea16b4179262e2a ))

- **scss**: tailwind intellisense | ([b4b9570](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/b4b9570a4d69960ca6f05cf410c03a959a3af223 ))

- **sidebar**: set sidebar to stay on top z index stack | ([e45ac49](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/e45ac4901cc08074652a5494ce520778b8c3e961 ))

- **sidebar**: get side bar full width while modal is open | ([2530f22](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/2530f22acd02b6505ce7f45cb25c4d788745e1d9 ))

- **dockerfile**: rm mocks | ([cceedb0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/cceedb0d09281811b4764ee8f03465c39dad2fce ))

- **dockerfile**: rm mocks | ([2e3b4ef](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/2e3b4efb4327e171216ccf054339b40711a29316 ))

- push renamed files from casing changes | ([0fdbddf](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/0fdbddff345a158883335617528528dbf03fb2f8 ))

- push renamed files from casing changes | ([1681767](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/16817673353459f9fdff509ef9bc32ac92b3e0dc ))

- fix lint warning | ([4dd776f](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/4dd776fcf05cc10b33ac3dd0594c86c91411fd4e ))

- fix lint warning | ([848595b](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/848595b26676e8c19796b990605e7cd3f53a7253 ))

- **iconssvgcomposant**: import padlock icon | ([04e2a7c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/04e2a7c3d3040a6f33196d6bce5a3eeaece90045 ))

- **iconssvgcomposant**: import padlock icon | ([bf77ad7](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/bf77ad70fda7d32042bd3ae6c882e8a5a8950d61 ))

- **rolemenu**: close submenu on click | ([7946f32](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/7946f327b0b272fe4a56bd145a18936af60b94f1 ))

- **ordernavbar**: use ref instead of id for breakpoint | ([563b7a6](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/563b7a60761584f81af3bea617e4c879362f0158 ))

- **workflows**: use ternary op for timeline | ([6384f84](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/6384f843c1479f114c1794d18996eccf837f472f ))

- **workflows**: correct grid & change width to % | ([ccac38c](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/ccac38c029fedeba209cd521aa65d779fcecff3e ))

- **login**: fix page render of login and fiber order | ([8f75ad0](https://gitlab.adm.internal.proteam.iliad.fr/front/proui/-/commit/8f75ad07d16bf6d56dec38d62aecabf5d7ad16fb ))

