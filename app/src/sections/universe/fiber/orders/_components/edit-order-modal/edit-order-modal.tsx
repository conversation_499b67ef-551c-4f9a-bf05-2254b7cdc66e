import { useState, useEffect, useCallback } from 'react';
// utils
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
// components
import { Dialog, DialogContent, DialogFooter, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/hook-form/form-provider';
// sections
import ReferentielIdentificationTab from './tabs/referentiel-identification-tab';
import ReferentielGeographiqueTab from './tabs/referentiel-geographique-tab';
import ReferentielOptiqueTab from './tabs/referentiel-optique-tab';
import OffreCommercialeTab from './tabs/offre-commerciale-tab';
import AutreTab from './tabs/autre-tab';
// hooks
import { useOrder } from '@/hooks/queries/use-order';
// api
import { useEditOrder } from '@/api/mutations/fiber/order.mutation';
// types
import type { EditOrderModalProps } from './types';
import { editOrderSchema, type EditOrderFormData } from './schema';
import type { TFiberPutOrderParams } from '@/types/params';

// ----------------------------------------------------------------------

const transformFormDataToApiData = (formData: EditOrderFormData, order: any): TFiberPutOrderParams => {
	const apiData: TFiberPutOrderParams = {};

	// Basic fields
	if (formData.jetonCommande !== order?.reference) {
		apiData.reference = formData.jetonCommande;
	}
	if (formData.referenceJFP !== order?.retailerReference) {
		apiData.retailerReference = formData.referenceJFP;
	}
	if (formData.referenceClientB2C !== order?.b2cReference) {
		apiData.b2cReference = formData.referenceClientB2C;
	}
	if (formData.autoriserMigrationB2CB2B !== order?.allowMigration) {
		apiData.allowMigration = formData.autoriserMigrationB2CB2B;
	}
	if (formData.modeAutomatique !== order?.autoMode) {
		apiData.autoMode = formData.modeAutomatique;
	}
	if (formData.vip !== order?.extraTracking) {
		apiData.extraTracking = formData.vip;
	}
	if (formData.provenance !== order?.acquisition) {
		apiData.acquisition = formData.provenance as any;
	}
	if (formData.garantiesSAV !== order?.sla) {
		apiData.sla = formData.garantiesSAV as any;
	}

	// Company
	if (formData.entrepriseCliente !== order?.company?.name) {
		apiData.company = { name: formData.entrepriseCliente };
	}

	// Endpoint address
	const iwId = parseInt(formData.iwAdresseId);
	if (!isNaN(iwId) && iwId !== order?.endpointAddress?.iwId) {
		apiData.endpointAddress = { iwId };
	}

	// Endpoint building
	if (formData.batimentIMB !== order?.endpointBuilding?.imb) {
		apiData.endpointBuilding = { imb: formData.batimentIMB };
	}

	// OIS data
	const currentOis = order?.currentOis || order?.ois?.[0];
	if (currentOis) {
		const oisChanged =
			formData.pm !== currentOis.pmName ||
			formData.ptoARaccorder !== currentOis.ptoName ||
			formData.pmt !== currentOis.pmTechnicalName ||
			formData.referenceAPCEtage !== currentOis.floorName ||
			formData.referenceAPCEscalier !== currentOis.stairCaseName ||
			formData.referenceAPCBatiment !== currentOis.buildingName;

		if (oisChanged) {
			apiData.ois = [
				{
					'@id': currentOis['@id'],
					pmName: formData.pm || currentOis.pmName,
					ptoName: formData.ptoARaccorder || currentOis.ptoName,
					pmTechnicalName: formData.pmt || currentOis.pmTechnicalName,
					floorName: formData.referenceAPCEtage || currentOis.floorName,
					stairCaseName: formData.referenceAPCEscalier || currentOis.stairCaseName,
					buildingName: formData.referenceAPCBatiment || currentOis.buildingName
				}
			];
		}
	}

	return apiData;
};

export default function EditOrderModal({ isOpen, onClose }: EditOrderModalProps) {
	const [activeTab, setActiveTab] = useState('referentiel-identification');
	const { order, queryOrder } = useOrder();
	const { isLoading } = queryOrder;

	// Extract order ID from the @id field
	const orderId = order?.['@id']?.split('/').pop() || '';
	const updateOrderMutation = useEditOrder({ orderId });

	const getDefaultValues = useCallback(
		(orderData?: typeof order): EditOrderFormData => ({
			// Tab 1: Référentiel identification
			jetonCommande: orderData?.reference || '',
			entrepriseCliente: orderData?.company?.name || '',
			retailer: typeof orderData?.retailer === 'string' ? orderData.retailer : orderData?.retailer?.name || '',
			referenceBDC: '',
			referenceEP: '',
			referenceJFP: orderData?.retailerReference || '',
			entrepriseDistributeur: orderData?.company?.name || '',

			// Tab 2: Référentiel géographique
			iwAdresseId: orderData?.endpointAddress?.iwId?.toString() || '',
			latitude: orderData?.endpointAddress?.latitude || orderData?.endpointAddress?.geoPointLat?.toString() || '',
			longitude: orderData?.endpointAddress?.longitude || orderData?.endpointAddress?.geoPointLon?.toString() || '',
			complementAdresse: orderData?.endpointAddress?.street2 || '',
			batimentIMB: orderData?.endpointBuilding?.imb || '',
			referenceAPCBatiment: orderData?.currentOis?.buildingName || '',
			referenceAPCEscalier: orderData?.currentOis?.stairCaseName || '',
			referenceAPCEtage: orderData?.currentOis?.floorName || '',

			// Tab 3: Référentiel optique
			commandeMultiAcces: orderData?.multiaccess || false,
			autoriserMigrationB2CB2B: orderData?.allowMigration || false,
			pm: orderData?.currentOis?.pmName || '',
			ptoARaccorder: orderData?.currentOis?.ptoName || 'Inconnue',
			ptoAConserver: orderData?.multiaccess ? orderData?.existingPto || 'Inconnue' : '',
			referenceClientB2C: orderData?.b2cReference || '',
			pmt: orderData?.currentOis?.pmTechnicalName || '',

			// Tab 4: Offre commerciale optique
			provenance: orderData?.acquisition || '',
			venteIndirecteCanal: '',
			garantiesSAV: orderData?.sla || '',
			typeOffre: orderData?.offerOrders?.[0]?.offer?.name?.includes('FTTH') ? 'FTTH' : '',
			freeboxPro1: orderData?.products?.some((product) => product.name === 'Freebox Pro v1') || false,
			freeboxPro2: orderData?.products?.some((product) => product.name === 'FREEBOX_PRO_V2') || false,
			kitMiseEnBaie1U: orderData?.products?.some((product) => product.name === 'Kit de mise en baie 1U') || false,
			comsProUCaaS: orderData?.products?.some((product) => product.name === 'Coms Pro') || false,
			supportPremium: orderData?.products?.some((product) => product.name === 'Support Premium') || false,
			disqueDurNVMe1To: orderData?.products?.some((product) => product.name === 'Disque dur 1To') || false,
			backup4G: orderData?.products?.some((product) => product.name === 'Backup 4G v1') || false,
			repeteurWIFIQuantity: orderData?.products?.filter((product) => product.name === 'Répéteur wifi 6').length || 0,

			// Tab 5: Autre
			commandeTest: false,
			modeAutomatique: orderData?.autoMode || false,
			vip: orderData?.extraTracking || false
		}),
		[]
	);

	const methods = useForm<EditOrderFormData>({
		resolver: zodResolver(editOrderSchema),
		defaultValues: getDefaultValues()
	});

	const { handleSubmit, reset, formState } = methods;

	console.log('🔍 Form errors:', formState.errors);
	console.log('🔍 Form is valid:', formState.isValid);
	console.log('🔍 Form is submitting:', formState.isSubmitting);

	// Reset form with order data when it becomes available
	useEffect(() => {
		if (order && !isLoading) {
			console.log('EditOrderModal - Order products:', order.products);
			const defaultValues = getDefaultValues(order);
			console.log('EditOrderModal - Default values:', defaultValues);
			reset(defaultValues);
		}
	}, [order, isLoading, reset, getDefaultValues]);

	const onSubmit = (data: EditOrderFormData) => {
		console.log('🚀 onSubmit called with data:', data);
		console.log('🚀 Current order:', order);
		console.log('🚀 Order ID:', orderId);

		if (!order) {
			console.error('❌ No order found');
			return;
		}

		if (!orderId) {
			console.error('❌ No order ID found');
			return;
		}

		const apiData = transformFormDataToApiData(data, order);
		console.log('🔄 Transformed API data:', apiData);

		if (Object.keys(apiData).length > 0) {
			console.log('✅ Changes detected, sending API request...');
			updateOrderMutation.mutate(apiData, {
				onSuccess: () => {
					console.log('✅ API request successful');
					onClose();
					reset();
				},
				onError: (error) => {
					console.error('❌ API request failed:', error);
				}
			});
		} else {
			console.log('i No changes detected, closing modal');
			onClose();
		}
	};

	const handleClose = () => {
		reset();
		setActiveTab('referentiel-identification');
		onClose();
	};

	return (
		<Dialog open={isOpen} onOpenChange={handleClose}>
			<DialogContent className='max-w-4xl h-[635px] overflow-hidden flex flex-col bg-[#FBFCFE] dark:bg-[#141414]'>
				<DialogHeader>
					<DialogTitle>Modifier les paramètres de commande</DialogTitle>
				</DialogHeader>

				{isLoading ? (
					<div className='flex-1 flex items-center justify-center'>
						<div className='text-center'>
							<div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4'></div>
							<p className='text-muted-foreground'>Chargement des données de la commande...</p>
						</div>
					</div>
				) : (
					<Form methods={methods} onSubmit={handleSubmit(onSubmit)} className='flex-1 overflow-hidden flex flex-col'>
						<Tabs value={activeTab} onValueChange={setActiveTab} className='flex-1 flex flex-col'>
							<TabsList variant='underline'>
								<TabsTrigger variant='underline' value='referentiel-identification'>
									Référentiel identification
								</TabsTrigger>
								<TabsTrigger variant='underline' value='referentiel-geographique'>
									Référentiel géographique
								</TabsTrigger>
								<TabsTrigger variant='underline' value='referentiel-optique'>
									Référentiel optique
								</TabsTrigger>
								<TabsTrigger variant='underline' value='offre-commerciale'>
									Offre commerciale optique
								</TabsTrigger>
								<TabsTrigger variant='underline' value='autre'>
									Autre
								</TabsTrigger>
							</TabsList>

							<div className='overflow-y-scroll mt-4 p-0.5 flex-1'>
								<TabsContent value='referentiel-identification' className='mt-0'>
									<ReferentielIdentificationTab />
								</TabsContent>

								<TabsContent value='referentiel-geographique' className='mt-0'>
									<ReferentielGeographiqueTab />
								</TabsContent>

								<TabsContent value='referentiel-optique' className='mt-0'>
									<ReferentielOptiqueTab />
								</TabsContent>

								<TabsContent value='offre-commerciale' className='mt-0'>
									<OffreCommercialeTab />
								</TabsContent>

								<TabsContent value='autre' className='mt-0'>
									<AutreTab />
								</TabsContent>
							</div>
						</Tabs>

						<DialogFooter className='flex justify-end space-x-4 mt-4'>
							<Button variant='outline' onClick={handleClose} type='button'>
								Annuler
							</Button>
							<Button
								type='submit'
								disabled={updateOrderMutation.isPending || isLoading}
								onClick={() => console.log('🔘 Submit button clicked!')}>
								{updateOrderMutation.isPending ? 'Enregistrement...' : 'Enregistrer'}
							</Button>
						</DialogFooter>
					</Form>
				)}

				{isLoading && (
					<DialogFooter className='flex justify-end space-x-4'>
						<Button variant='outline' onClick={handleClose}>
							Annuler
						</Button>
						<Button disabled>Enregistrer</Button>
					</DialogFooter>
				)}
			</DialogContent>
		</Dialog>
	);
}
