import type React from 'react';
// utils
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { Badge, type TBadgeVariant } from '@/components/ui/badge.tsx';

// ----------------------------------------------------------------------

export type TypologyItemState = 'ok' | 'blocked' | 'waiting' | 'inprogress';

const STATE_CONFIG: Record<TypologyItemState, { variant: TBadgeVariant; label: string }> = {
	ok: { variant: 'green', label: 'OK' },
	blocked: { variant: 'red', label: 'Bloqué' },
	waiting: { variant: 'yellow', label: 'Attente' },
	inprogress: { variant: 'blue', label: 'En cours' }
};

const typologyItemVariants = cva('flex gap-2 p-2 rounded-xl justify-between items-center transition-all duration-200', {
	variants: {
		variant: {
			green: 'bg-[#DBF0EA] text-[#1B806A]',
			red: 'bg-[#FCE1DD] text-[#B71D18]',
			gray: 'bg-[#E0E0E0] text-[#8D8D8D]',
			yellow: 'bg-[#FCEFD5] text-[#B76E00]',
			blue: 'bg-[#DEE7FF] text-[#1939B7]'
		},
		size: {
			md: 'w-[11.4%]',
			sm: 'w-[5.1%]'
		},
		isActive: {
			true: 'bg-white border border-primary border-2',
			false: ''
		}
	},
	compoundVariants: [
		{
			size: 'md',
			isActive: true,
			className: 'w-[24%]'
		},
		{
			size: 'sm',
			isActive: true,
			className: 'w-[15%]'
		}
	],
	defaultVariants: {
		variant: 'gray',
		size: 'md',
		isActive: false
	}
});

const chipVariants = cva('w-2 min-w-2 h-full rounded-full', {
	variants: {
		variant: {
			green: 'bg-[#DBF0EA]',
			red: 'bg-[#FCE1DD]',
			gray: 'bg-[#E0E0E0]',
			yellow: 'bg-[#FCEFD5]',
			blue: 'bg-[#DEE7FF]'
		},
		isActive: {
			true: '',
			false: 'bg-white'
		}
	},
	defaultVariants: {
		variant: 'gray',
		isActive: false
	}
});

export interface TypologyItemProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof typologyItemVariants> {
	label: string;
	value: string;
	state?: TypologyItemState;
}

export default function TypologyItem({
	label,
	value,
	variant = 'gray',
	size = 'md',
	isActive = false,
	state,
	className,
	...props
}: TypologyItemProps) {
	return (
		<div className={cn(typologyItemVariants({ variant, size, isActive }), className)} {...props}>
			<div className='flex gap-2 h-full overflow-hidden text-ellipsis whitespace-nowrap'>
				<div className={cn(chipVariants({ variant, isActive }))} />
				<div className='flex flex-col overflow-hidden'>
					<div className='text-[11px] leading-3 font-bold uppercase'>{label}</div>
					<div className='text-md font-semibold leading-5 mt-0.5 overflow-hidden text-foreground overflow-ellipsis'>{value}</div>
				</div>
			</div>
			{isActive && state && (
				<Badge variant={STATE_CONFIG[state].variant} size={'sm'} className='mr-1'>
					{STATE_CONFIG[state].label}
				</Badge>
			)}
		</div>
	);
}
