import type React from 'react';
import { Dice4, Dice6, Droplets03, Globe02, GridDotsVerticalCenter, Key01, TextInput } from '@tools/reactor-icons';
import { Badge } from '@/components/ui/badge.tsx';
import { Button } from '@/components/ui/button.tsx';

export default function CollecteTab(): React.ReactElement {
	return (
		<div className='flex gap-2 w-full p-4 bg-card rounded-xl mt-1 flex-col'>
			<div className='flex justify-between items-center'>
				<h3 className='text-lg font-semibold'>Collecte & Peering</h3>
				<div className='flex gap-2'>
					<Button variant='outline' size='sm'>
						Historique IP
					</Button>
				</div>
			</div>
			<div className='grid grid-cols-3 gap-4'>
				<div className='col-span-1'>
					<p className='text-sm text-[#6f6f6f] mb-2'>
						Collecte du <span className='font-medium text-[#161616]'>traffic</span>
					</p>
					<div className='border border-[#e5e5e5] rounded-lg p-2'>
						<div className='space-y-2'>
							<div className={'flex gap-2 items-center'}>
								<Globe02 size={18} className={'text-gray-400'} />
								<p className='text-gray-400 text-[12px]'>Zone de collecte</p>
								<p className={'text-sm'}>Anycast</p>
							</div>
							<div className={'flex gap-2 items-center'}>
								<Key01 size={18} className={'text-gray-400'} />
								<p className='text-gray-400 text-[12px]'>Speed VLAN</p>
								<p className={'text-sm'}>1096515368</p>
							</div>
							<div className={'flex gap-2 items-center'}>
								<GridDotsVerticalCenter size={18} className={'text-gray-400'} />
								<p className='text-gray-400 text-[12px]'>Étage</p>
								<Badge variant='gray' size={'sm'}>
									Actif
								</Badge>
							</div>
						</div>
					</div>
				</div>
				<div className='col-span-1'>
					<div className='text-sm text-[#6f6f6f] mb-2 flex gap-2'>
						<div>
							{' '}
							Configuration <span className='font-medium text-[#161616]'>VLAN</span>
						</div>
						<Badge variant={'green'} size={'sm'}>
							Actif
						</Badge>
						31 déc. 2024 13:59
					</div>
					<div className='border border-[#e5e5e5] rounded-lg p-2'>
						<div className='space-y-2'>
							<div className={'flex gap-2 items-center'}>
								<TextInput size={18} className={'text-gray-400'} />
								<p className='text-gray-400 text-[12px]'>Supplier</p>
								<p className={'text-sm'}>107</p>
							</div>
							<div className={'flex gap-2 items-center'}>
								<TextInput size={18} className={'text-gray-400'} />
								<p className='text-gray-400 text-[12px]'>Customer</p>
								<p className={'text-sm'}>64901</p>
							</div>
							<div className={'flex gap-2 items-center'}>
								<TextInput size={18} className={'text-gray-400'} />
								<p className='text-gray-400 text-[12px]'>User</p>
								<p className={'text-sm'}>409</p>
							</div>
						</div>
					</div>
				</div>
				<div className='col-span-1'>
					<div className='text-sm text-[#6f6f6f] mb-2 flex gap-2'>
						<div>
							{' '}
							Configuration <span className='font-medium text-[#161616]'>IP</span>
						</div>
						<Badge variant={'green'} size={'sm'}>
							Provisionné
						</Badge>
						31 déc. 2024 13:59
					</div>
					<div className='border border-[#e5e5e5] rounded-lg p-2'>
						<div className='space-y-2'>
							<div className={'flex gap-2 items-center'}>
								<Dice4 variant={'solid'} size={18} className={'text-gray-400'} />
								<p className='text-gray-400 text-[12px]'>IP v4 privée</p>
								<p className={'text-sm'}>*************</p>
							</div>
							<div className={'flex gap-2 items-center'}>
								<Dice4 size={18} className={'text-gray-400'} />
								<p className='text-gray-400 text-[12px]'>IP v4 publique</p>
								<p className={'text-sm'}>************</p>
								<Badge variant={'gray'} size={'sm'}>
									0-16383
								</Badge>
							</div>
							<div className={'flex gap-2 items-center'}>
								<Droplets03 size={18} className={'text-gray-400'} />
								<p className='text-gray-400 text-[12px]'>Attribution</p>
								<p className={'text-sm'}>Dynamique</p>
							</div>
							<div className={'flex gap-2 items-center'}>
								<Dice6 size={18} className={'text-gray-400'} />
								<p className='text-gray-400 text-[12px]'>IP v6 publique</p>
								<p className={'text-sm'}>2010:0000:0062:AA98:</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
