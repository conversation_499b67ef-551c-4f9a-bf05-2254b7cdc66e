import { forwardRef, useState, type ForwardRefExoticComponent, type RefAttributes } from 'react';
import { useOrder } from '@/hooks/queries/use-order.ts';
import { Badge } from '@/components/ui/badge.tsx';
import TypologyItem from './typology-item/typology-item';
import {
	CollecteTab,
	NROTab,
	CablesTab,
	PMTab,
	PBOTab,
	PTOTab,
	ONUTab,
	EquipementTab
} from './tab-contents';

type TabType = 'collecte' | 'nro' | 'cables' | 'pm' | 'pbo' | 'pto' | 'onu' | 'equipement';

const LineTypology: ForwardRefExoticComponent<RefAttributes<HTMLElement>> = forwardRef((_, ref) => {
	const { order, vlan, rop, network, ip, oi, appointment, offerOrder, linkState, onu } = useOrder();
	const [activeTab, setActiveTab] = useState<TabType>('collecte');

	const renderTabContent = () => {
		switch (activeTab) {
			case 'collecte':
				return <CollecteTab />;
			case 'nro':
				return <NROTab />;
			case 'cables':
				return <CablesTab />;
			case 'pm':
				return <PMTab />;
			case 'pbo':
				return <PBOTab />;
			case 'pto':
				return <PTOTab />;
			case 'onu':
				return <ONUTab />;
			case 'equipement':
				return <EquipementTab />;
			default:
				return <CollecteTab />;
		}
	};

	return (
		<section id='typology' ref={ref} className={'py-3'}>
			<div className='flex justify-between items-center mb-4'>
				<div className='flex items-center gap-2'>
					<div className='text-md font-semibold'>Typologie de ligne</div>

					{oi?.technology && (
						<Badge variant='gray' size={'sm'}>
							{oi?.technology}
						</Badge>
					)}
				</div>
			</div>
			<div className='flex flex-col gap-1'>
				<div className='grid grid-cols-8 gap-2 w-full'>
					<TypologyItem
						label='Collecte'
						value='Anycast - Paris'
						variant='green'
						state='ok'
						isActive={activeTab === 'collecte'}
						onClick={() => setActiveTab('collecte')}
					/>
					<TypologyItem
						label='NRO'
						value='ROQ75'
						variant='red'
						isActive={activeTab === 'nro'}
						onClick={() => setActiveTab('nro')}
					/>
					<TypologyItem
						label='Cables'
						value='2'
						variant='gray'
						size='sm'
						isActive={activeTab === 'cables'}
						onClick={() => setActiveTab('cables')}
					/>
					<TypologyItem
						label='PM'
						value='FI-00000-0000'
						variant='yellow'
						isActive={activeTab === 'pm'}
						onClick={() => setActiveTab('pm')}
					/>
					<TypologyItem
						label='PBO'
						value='FI-00000-0000'
						variant='blue'
						state='inprogress'
						isActive={activeTab === 'pbo'}
						onClick={() => setActiveTab('pbo')}
					/>
					<TypologyItem
						label='PTO'
						value='FI-00000-0000'
						variant='green'
						isActive={activeTab === 'pto'}
						onClick={() => setActiveTab('pto')}
					/>
					<TypologyItem
						label='ONU'
						value='388773561349856329987654345678987654'
						variant='green'
						isActive={activeTab === 'onu'}
						onClick={() => setActiveTab('onu')}
					/>
					<TypologyItem
						label='Équipement'
						value='Freebox Pro 2'
						variant='yellow'
						isActive={activeTab === 'equipement'}
						onClick={() => setActiveTab('equipement')}
					/>
				</div>
				{renderTabContent()}
			</div>
		</section>
	);
});

export default LineTypology;
