import { forwardRef, type ForwardRefExoticComponent, type RefAttributes } from 'react';
import { useOrder } from '@/hooks/queries/use-order.ts';
import { Badge } from '@/components/ui/badge.tsx';
import TypologyItem from './typology-item/typology-item';

const LineTypology: ForwardRefExoticComponent<RefAttributes<HTMLElement>> = forwardRef((_, ref) => {
	const { order, vlan, rop, network, ip, oi, appointment, offerOrder, linkState, onu } = useOrder();

	return (
		<section id='typology' ref={ref} className={'py-3'}>
			<div className='flex justify-between items-center mb-4'>
				<div className='flex items-center gap-2'>
					<div className='text-md font-semibold'>Typologie de ligne</div>

					{oi?.technology && (
						<Badge variant='gray' size={'sm'}>
							{oi?.technology}
						</Badge>
					)}
				</div>
			</div>
			<div className='flex flex-col gap-1'>
				<div className='flex gap-2 w-full'>
					<TypologyItem label='Collecte' value='Anycast - Paris' variant='green' state='ok' />
					<TypologyItem label='NRO' value='ROQ75' variant='red' />
					<TypologyItem label='Cables' value='2' variant='gray' size='sm' />
					<TypologyItem label='PM' value='FI-00000-0000' variant='yellow' />
					<TypologyItem label='PBO' value='FI-00000-0000' variant='blue' isActive state='inprogress' />
					<TypologyItem label='PTO' value='FI-00000-0000' variant='green' />
					<TypologyItem label='ONU' value='388773561349856329987654345678987654' variant='green' />
					<TypologyItem label='Équipement' value='Freebox Pro 2' variant='yellow' />
				</div>
				<div className='flex gap-2 w-full p-4 bg-card rounded-xl mt-1 font-semibold text-lg'>Collecte</div>
			</div>
		</section>
	);
});

export default LineTypology;
