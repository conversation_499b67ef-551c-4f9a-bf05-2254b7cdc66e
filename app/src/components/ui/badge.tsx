import type * as React from 'react';
// utils
import { cva, type VariantProps } from 'class-variance-authority';
// others
import { cn } from '@/lib/utils';

// ----------------------------------------------------------------------

const badgeVariants = cva(
	'inline-flex items-center rounded-md border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 h-fit ',
	{
		variants: {
			variant: {
				default: 'border-transparent bg-primary text-primary-foreground hover:bg-primary/80',
				secondary: 'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',
				destructive: 'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',
				outline: 'text-foreground',
				blue: 'border-transparent bg-blue-100 text-blue-700',
				green: 'border-transparent bg-green-100 text-green-700',
				gray: 'border-transparent bg-gray-200 text-gray-700',
				yellow: 'border-transparent bg-yellow-100 text-yellow-700',
				orange: 'border-transparent bg-orange-100 text-orange-700',
				red: 'border-transparent bg-red-100 text-red-700',
				'dark-red': 'border-transparent bg-red-100 text-red-700',
				'solid-blue': 'border-transparent bg-blue-500 text-white',
				'solid-green': 'border-transparent bg-green-500 text-white',
				'solid-gray': 'border-transparent bg-gray-500 text-white',
				'solid-yellow': 'border-transparent bg-yellow-500 text-grey-800',
				'solid-orange': 'border-transparent bg-orange-500 text-white',
				'solid-red': 'border-transparent bg-red-500 text-white',
				'solid-dark-red': 'border-transparent bg-[#B20000] text-white',
				'solid-black': 'border-transparent bg-black text-white'
			},
			size: {
				sm: 'px-2 py-0.5 text-xs',
				md: 'px-2.5 py-0.5 text-xs',
				lg: 'px-3 py-1 text-sm',
				xl: 'px-3.5 py-1.5 text-base'
			}
		},
		defaultVariants: {
			variant: 'default',
			size: 'md'
		}
	}
);

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, size, ...props }: BadgeProps) {
	return <div className={cn(badgeVariants({ variant, size }), className)} {...props} />;
}

export type TBadgeVariant = VariantProps<typeof badgeVariants>['variant'];
export type TBadgeSize = VariantProps<typeof badgeVariants>['size'];

export { Badge, badgeVariants };
